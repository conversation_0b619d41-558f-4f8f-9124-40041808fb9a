# 任务完成报告模板 - TASK-{task_id} - {feature_name}

## 基本信息
- **任务ID**: TASK-{task_id}
- **功能名称**: {feature_name}
- **任务标题**: {task_title}
- **负责代理**: spec-developer
- **完成时间**: {completion_timestamp}
- **耗时**: {duration}

## 任务详情

### 原始需求
{task_description}

### 实现方案
{implementation_approach}

### 技术选择
- **主要技术**: {technologies_used}
- **依赖库**: {dependencies_added}
- **架构模式**: {patterns_applied}

## 代码变更

### 新增文件
- {new_files_list}

### 修改文件
- {modified_files_list}

### 代码统计
- **新增行数**: {lines_added}
- **修改行数**: {lines_modified}
- **删除行数**: {lines_deleted}

## 测试结果

### 单元测试
- **测试用例数**: {unit_test_count}
- **通过率**: {unit_test_pass_rate}
- **覆盖率**: {code_coverage}

### 集成测试
- **测试场景**: {integration_test_scenarios}
- **测试结果**: {integration_test_results}

## 代码审查

### 审查结果
- **审查状态**: ✅ 通过 / ❌ 不通过
- **审查评分**: {review_score}/100
- **主要问题**: {review_issues}
- **改进建议**: {review_suggestions}

### 修复记录
{fix_records}

## 性能优化

### 性能分析
- **响应时间**: {response_time}
- **内存使用**: {memory_usage}
- **CPU使用**: {cpu_usage}

### 优化措施
{performance_optimizations}

## 安全扫描

### 扫描结果
- **高危漏洞**: {critical_vulnerabilities}
- **中危漏洞**: {high_vulnerabilities}
- **低危漏洞**: {medium_vulnerabilities}

### 修复措施
{security_fixes}

## 质量指标
- **代码质量评分**: {quality_score}/100
- **圈复杂度**: {complexity_score}
- **重复率**: {duplication_rate}%
- **技术债务**: {technical_debt_score}

## 遇到的问题

### 问题1: {problem_title}
- **描述**: {problem_description}
- **解决方案**: {solution_description}
- **耗时**: {problem_resolution_time}

## Git Flow记录

### 分支管理
- **Feature分支**: feature/task-{task_id}-{feature_name}
- **创建时间**: {branch_create_time}
- **合并时间**: {branch_merge_time}

### 提交记录
- **提交哈希**: {git_commit_hash}
- **提交信息**: {git_commit_message}
- **文件变更**: {git_files_changed}

## 监控数据更新

### 实时监控
- **代理状态**: 已完成
- **质量指标**: 已更新
- **性能数据**: 已记录

### 历史记录
- **历史数据**: 已存档
- **趋势分析**: 已更新
- **告警处理**: 已记录

## 经验总结

### 成功经验
{success_lessons}

### 改进建议
{improvement_suggestions}

### 最佳实践
{best_practices}

## 下一步行动

### 后续任务
- {next_tasks}

### 依赖关系
- {task_dependencies}

### 风险提醒
- {risk_warnings}

## 相关链接
- **Git提交**: {git_commit_url}
- **相关任务**: {related_tasks}
- **参考文档**: {reference_docs}
- **测试报告**: {test_report_url}

## 验证清单
- [ ] 代码编译通过
- [ ] 所有测试通过
- [ ] 代码审查通过
- [ ] 性能指标达标
- [ ] 安全扫描通过
- [ ] 文档已更新
- [ ] Git提交完成
- [ ] 监控数据更新
- [ ] 任务状态更新

## 签名确认
- **开发者**: spec-developer
- **审查者**: spec-reviewer
- **测试者**: spec-tester
- **完成时间**: {final_completion_time}

---

**文件命名规则**: 此报告文件名格式为 `task-{task_id}-{feature_name}-completion-report.md`，确保与功能的关联性和可追溯性。
