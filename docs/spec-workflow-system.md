# 规格代理工作流系统

## 概述

规格代理工作流系统结合了BMAD经过验证的多代理架构和Claude Code的子代理功能，创建了一个自动化的、质量门控的开发管道。该系统通过专业化AI代理的协调序列，将复杂项目从概念转化为生产就绪的代码。

## 核心理念

### 1. **专业化专长**

每个代理都是专注于开发生命周期特定方面的领域专家，在隔离的上下文中运行，以保持清晰度并防止关注点的交叉污染。

### 2. **文档驱动的工作流**

每个阶段都产生结构化的工件，作为后续阶段的输入，确保整个开发过程的可追溯性和一致性。

### 3. **质量门控**

自动化验证检查点确保每个阶段在继续之前满足定义的质量标准，具有智能反馈循环以持续改进。

### 4. **迭代卓越**

系统支持线性进展和迭代改进，自动循环改进循环直到满足质量阈值。

## 系统架构

```mermaid
graph TD
    A[用户请求] --> B[工作流编排器]
    B --> C[规划阶段]
    C --> D[spec-analyst]
    D --> E[spec-architect]
    E --> F[spec-planner]

    F --> G{质量门控 1}
    G -->|通过| H[开发阶段]
    G -->|失败| D

    H --> I[spec-developer]
    I --> J[spec-tester]

    J --> K{质量门控 2}
    K -->|通过| L[验证阶段]
    K -->|失败| I

    L --> M[spec-reviewer]
    M --> N[spec-validator]

    N --> O{质量门控 3}
    O -->|通过| P[部署就绪]
    O -->|失败| Q{确定修复路径}

    Q --> R[返回规划]
    Q --> S[返回开发]

    R --> D
    S --> I

    P --> T[完整包]
    T --> U[文档]
    T --> V[代码]
    T --> W[测试]
    T --> X[部署脚本]

    style B fill:#1a73e8,color:#fff
    style G fill:#f9ab00,color:#fff
    style K fill:#f9ab00,color:#fff
    style O fill:#f9ab00,color:#fff
    style P fill:#34a853,color:#fff
```

## 代理角色

### 规划阶段代理

#### 1. spec-analyst（规格分析师）

- **目的**: 需求分析和项目范围界定
- **职责**:
  - 引出和澄清需求
  - 创建用户故事和验收标准
  - 执行市场和竞争分析
  - 生成项目简介
- **输出**: `requirements.md`, `project-brief.md`, `user-stories.md`

#### 2. spec-architect（规格架构师）

- **目的**: 系统设计和技术架构
- **职责**:
  - 设计系统架构
  - 定义技术栈
  - 创建组件图
  - 规划数据模型和API
- **输出**: `architecture.md`, `tech-stack.md`, `api-spec.md`

#### 3. spec-planner（规格规划师）

- **目的**: 任务分解和实施规划
- **职责**:
  - 创建详细任务列表
  - 定义实施顺序
  - 估算复杂度和工作量
  - 规划测试策略
- **输出**: `tasks.md`, `test-plan.md`, `implementation-plan.md`

### 开发阶段代理

#### 4. spec-developer（规格开发者）

- **目的**: 代码实现
- **职责**:
  - 基于规格实现功能
  - 遵循架构模式
  - 编写清洁、可维护的代码
  - 创建单元测试
- **输出**: 源代码文件，单元测试

#### 5. spec-tester（规格测试员）

- **目的**: 综合测试
- **职责**:
  - 编写集成测试
  - 执行端到端测试
  - 安全测试
  - 性能测试
- **输出**: 测试套件，测试报告

### 验证阶段代理

#### 6. spec-reviewer（规格审查员）

- **目的**: 代码质量审查
- **职责**:
  - 最佳实践代码审查
  - 安全漏洞扫描
  - 性能优化建议
  - 文档完整性检查
- **输出**: `review-report.md`, 重构代码

#### 7. spec-validator（规格验证员）

- **目的**: 最终质量验证
- **职责**:
  - 验证需求合规性
  - 验证架构遵循性
  - 检查测试覆盖率
  - 评估生产就绪性
- **输出**: `validation-report.md`, 质量评分 (0-100%)

### 编排代理

#### 8. spec-orchestrator（规格编排器）

- **目的**: 工作流协调和管理
- **职责**:
  - 将任务路由到适当的代理
  - 管理质量门控
  - 处理反馈循环
  - 跟踪整体进度
- **输出**: `workflow-status.md`, 执行日志

## 质量门控系统

### 门控 1: 规划质量（spec-planner之后）

- **标准**:
  - 需求完整性 ≥ 95%
  - 架构可行性已验证
  - 所有用户故事都有验收标准
  - 任务分解是全面的
- **行动**: 如果失败，返回spec-analyst并提供具体反馈

### 门控 2: 开发质量（spec-tester之后）

- **标准**:
  - 所有测试通过
  - 代码覆盖率 ≥ 80%
  - 无关键安全漏洞
  - 满足性能基准
- **行动**: 如果失败，返回spec-developer并提供问题列表

### 门控 3: 生产就绪性（spec-validator之后）

- **标准**:
  - 整体质量评分 ≥ 95%
  - 所有需求已实现
  - 文档完整
  - 部署脚本已测试
- **行动**: 如果失败，确定是在规划阶段还是开发阶段修复

## 工作流命令

### 主要命令

```bash
# 启动新项目工作流
/agent-workflow <项目描述>

# 从现有需求开始
/agent-workflow --from-requirements <path/to/requirements.md>

# 从特定阶段开始
/agent-workflow --phase development --from-artifacts <path/to/artifacts>
```

### 控制命令

```bash
# 检查工作流状态
/spec-status

# 跳过特定代理
/agent-workflow --skip-agents spec-analyst,spec-tester

# 设置自定义质量阈值
/agent-workflow --quality-threshold 90

# 启用详细模式
/agent-workflow --verbose
```

### 阶段特定命令

```bash
# 仅规划
/spec-plan <项目描述>

# 从现有规格开发
/spec-develop <path/to/specifications>

# 验证现有代码
/spec-validate <path/to/project>
```

## 与现有工具的集成

### IDE集成

- 与任何支持Claude Code的IDE兼容
- 自动文件管理和组织
- 实时进度跟踪

### 版本控制

- Git友好的工件生成
- 自动提交建议
- 分支管理建议

### CI/CD管道

- 为CI准备的生成测试套件
- 各种平台的部署脚本
- 环境配置文件

## 最佳实践

### 1. **项目准备**

- 清晰的项目描述
- 现有文档（如果有）
- 定义技术约束
- 建立成功标准

### 2. **工作流执行**

- 让代理完成其阶段
- 在阶段之间审查工件
- 在提示时提供反馈
- 信任质量门控

### 3. **定制化**

- 根据项目需求调整质量阈值
- 为简单项目跳过代理
- 添加自定义验证标准
- 与现有流程集成

## 使用示例

### 简单Web应用

```bash
/agent-workflow 创建一个待办事项列表Web应用，使用React前端和Node.js后端，
支持用户认证、任务CRUD操作和实时更新
```

### 企业系统

```bash
/agent-workflow --quality-threshold 98 --verbose
开发一个企业资源规划系统，采用微服务架构，
支持库存管理、订单处理和财务报告
```

### 仅API服务

```bash
/agent-workflow --skip-agents spec-analyst
--from-requirements ./requirements/api-spec.md
构建一个用于支付处理的RESTful API服务，集成Stripe
```

## 相比传统开发的优势

### 相比手动开发

- **速度**: 从概念到代码快10倍
- **一致性**: 标准化的工件和模式
- **质量**: 自动化质量门控确保标准
- **文档**: 自动生成全面的文档

### 相比单一AI代理

- **专业性**: 每个领域的专业化代理
- **上下文**: 清洁、专注的上下文防止混乱
- **可扩展性**: 并行处理能力
- **可靠性**: 质量门控早期发现问题

### 相比单独的BMAD

- **自动化**: 完全自动化的工作流执行
- **集成**: 原生Claude Code子代理支持
- **灵活性**: 易于定制和扩展
- **性能**: 为现代AI能力优化

## 开始使用

1. **安装**: 将代理文件保存到`.claude/agents/`目录
2. **配置**: 在编排器中调整质量阈值
3. **第一个项目**: 用简单项目试用以了解流程
4. **迭代**: 根据需求优化代理提示

## 结论

规格代理工作流系统代表了AI辅助开发的演进，结合了BMAD经过验证的方法论的最佳实践和Claude Code强大的子代理功能。它将开发过程从一系列手动步骤转变为智能的、自动化的管道，持续交付高质量的结果。

通过利用协同工作的专业化AI代理，开发者可以专注于创造性问题解决，而系统处理协调、质量保证和文档的复杂性。结果是更快的开发周期、更高的代码质量和全面的文档——所有这些都只需最少的人工干预。

欢迎来到AI驱动开发的未来，在这里专业知识无限扩展，质量由设计保证。
