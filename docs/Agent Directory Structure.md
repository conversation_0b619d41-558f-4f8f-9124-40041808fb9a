# 代理目录结构

此目录包含Claude子代理系统的专业AI代理。代理按其领域专长和工作流角色进行组织。

## 目录组织

```
agent/
├── documentation/               # 系统文档和指南
│   ├── agent-workflow-system.md     # 完整工作流系统文档
│   ├── agent-workflow-usage-guide.md # 使用示例和最佳实践
├── spec-agents/                 # 规格工作流代理
│   ├── spec-orchestrator.md    # 主工作流协调器
│   ├── spec-analyst.md         # 需求分析专家
│   ├── spec-architect.md       # 系统架构设计师
│   ├── spec-planner.md         # 任务分解和规划
│   ├── spec-developer.md       # 实现专家
│   ├── spec-tester.md          # 全面测试专家
│   ├── spec-reviewer.md        # 代码审查专家
│   └── spec-validator.md       # 最终验证专家
│
├── frontend/                    # 前端专业代理
│   └── senior-frontend-architect.md # React/Vue/Next.js专家
│
├── backend/                     # 后端专业代理
│   └── senior-backend-architect.md  # Go/TypeScript后端专家
│
├── ui-ux/                       # UI/UX设计代理
│   └── ui-ux-master.md         # UI/UX设计和实现专家
│
└── refactor-agent.md           # 代码重构专家

```

## 代理类别

### 1. 规格工作流代理

自动化整个开发生命周期的核心工作流系统：

- **规划阶段**: spec-analyst → spec-architect → spec-planner
- **开发阶段**: spec-developer → spec-tester
- **验证阶段**: spec-reviewer → spec-validator
- **协调**: spec-orchestrator管理整个工作流

### 2. 领域专家

特定技术领域的专家代理：

- **前端**: 现代JavaScript框架、性能、可访问性
- **后端**: 分布式系统、API、数据库、安全
- **UI/UX**: 设计系统、用户体验、可访问性

### 3. 实用代理

- **refactor-agent**: 代码质量和重构专家

## 使用方法

### 基本工作流

```bash
# 启动完整项目工作流
使用 spec-orchestrator: 创建一个任务管理Web应用

# 使用特定专家
使用 senior-frontend-architect: 审查我的React组件架构
```

### 直接代理使用

```bash
# 规划阶段
使用 spec-analyst: 分析电商平台的需求

# 开发阶段
使用 spec-developer: 实现用户认证系统

# 审查阶段
使用 spec-reviewer: 审查认证实现
```

## 质量门控

工作流包含三个质量门控：

1. **规划门控** (95%阈值) - spec-planner之后
2. **开发门控** (80%阈值) - spec-tester之后
3. **生产门控** (85%阈值) - spec-validator之后

## 集成

代理可以协同工作：

- UI/UX大师 → 提供设计规格 → 规格工作流
- 高级后端架构师 → 增强 → 规格架构师设计
- 高级前端架构师 → 指导 → 规格开发者实现

## 最佳实践

1. **从spec-orchestrator开始** 完整项目
2. **使用领域专家** 获得特定专业知识
3. **遵循工作流** 确保一致质量
4. **利用质量门控** 确保标准
5. **启用并行执行** 用于大型项目

## 贡献

添加新代理时：

1. 放置在适当的类别目录中
2. 遵循YAML前置格式
3. 包含清晰的描述和工具要求
4. 用新代理更新此README
5. 测试与现有工作流的集成

## 版本历史

- v1.0: 初始规格工作流系统
- v1.1: 添加领域专家代理
- v1.2: 增强编排和质量门控
