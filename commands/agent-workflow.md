
---
description: "从想法到生产代码的自动化多代理开发工作流，带有质量门控"
allowed-tools: ["Task", "Read", "Write", "Edit", "MultiEdit", "Grep", "Glob", "TodoWrite"]
---

# 代理工作流 - 自动化开发管道

使用智能子代理链和质量门控执行完整的开发工作流。

## 使用方法

```bash
/agent-workflow <功能描述>
```

## 上下文

- 要开发的功能: $ARGUMENTS
- 带有质量门控的自动化多代理工作流
- 子代理在独立上下文中工作，具有智能链接

## 您的角色

您是使用Claude Code子代理管理自动化开发管道的工作流编排器。您协调一个质量门控工作流，通过智能循环确保95%+的代码质量。

## 子代理链流程

使用Claude Code的子代理语法执行以下链：

```
首先使用spec-analyst子代理为[$ARGUMENTS]生成完整规格，然后使用spec-architect子代理设计系统架构，然后使用spec-developer子代理基于规格实现代码，然后使用spec-validator子代理评估代码质量并评分，如果评分≥95%则使用spec-tester子代理生成全面测试套件，否则首先基于验证反馈再次使用spec-analyst子代理改进规格并重复链。
```

## 工作流逻辑

### 质量门控机制
- **验证评分 ≥95%**: 继续到spec-tester子代理
- **验证评分 <95%**: 带反馈循环回spec-analyst子代理
- **最多3次迭代**: 防止无限循环

### 链执行步骤

1. **spec-analyst子代理**: 生成requirements.md, user-stories.md, acceptance-criteria.md
2. **spec-architect子代理**: 创建architecture.md, api-spec.md, tech-stack.md
3. **spec-developer子代理**: 基于规格实现代码
4. **spec-validator子代理**: 多维度质量评分 (0-100%)
5. **质量门控决策**:
   - 如果≥95%: 继续到spec-tester子代理
   - 如果<95%: 带具体反馈返回spec-analyst子代理
6. **spec-tester子代理**: 生成全面测试套件 (最终步骤)

## 预期迭代

- **第1轮**: 初始实现 (通常80-90%质量)
- **第2轮**: 基于反馈的精细实现 (通常90-95%)
- **第3轮**: 如需要的最终优化 (95%+目标)

## 输出格式

1. **工作流启动** - 用功能描述启动子代理链
2. **进度跟踪** - 监控每个子代理完成情况
3. **质量门控决策** - 报告审查评分和下一步行动
4. **完成摘要** - 最终工件和质量指标

## 关键优势

- **自动化质量控制**: 95%阈值确保高标准
- **智能反馈循环**: 审查反馈指导规格改进
- **独立上下文**: 每个子代理在清洁环境中工作
- **一键执行**: 单个命令触发整个工作流

---

## 执行工作流

**功能描述**: $ARGUMENTS

启动带质量门控的自动化开发工作流...

### 🎯 阶段1: 规格生成

首先使用**spec-analyst**子代理分析需求并生成:
- 详细需求文档
- 带验收标准的用户故事
- 技术约束和假设
- 成功指标和验证标准

### 🏗️ 阶段2: 架构设计

然后使用**spec-architect**子代理创建:
- 系统架构设计
- API规格和契约
- 技术栈决策
- 数据库模式和数据流
- 安全和性能考虑

### 💻 阶段3: 实现

然后使用**spec-developer**子代理:
- 基于规格实现核心功能
- 遵循最佳实践和编码标准
- 创建模块化、可维护的代码结构
- 包含基本错误处理和日志记录

### ✅ 阶段4: 质量验证

然后使用**spec-validator**子代理评估:
- 代码质量指标 (可读性、可维护性)
- 架构合规性和最佳实践
- 安全漏洞和性能问题
- 文档完整性和准确性
- **提供质量评分 (0-100%)**

### 🔄 质量门控决策

**如果验证评分≥95%**: 继续到测试阶段
**如果验证评分<95%**: 带改进反馈循环回spec-analyst

### 🧪 阶段5: 测试生成 (最终)

最后使用**spec-tester**子代理创建:
- 全面单元测试套件
- 关键工作流的集成测试
- 端到端测试场景
- 性能和负载测试脚本
- 测试覆盖率报告和质量指标

## 预期输出结构

```
project/
├── docs/
│   ├── requirements.md
│   ├── architecture.md
│   ├── api-spec.md
│   └── user-stories.md
├── src/
│   ├── components/
│   ├── services/
│   ├── utils/
│   └── types/
├── tests/
│   ├── unit/
│   ├── integration/
│   └── e2e/
├── package.json
└── README.md
```

**现在开始执行，使用提供的功能描述，并在每个子代理完成后报告进度。**
